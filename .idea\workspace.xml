<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="23d2bed5-2b33-4bbe-9aab-6edeb00ded5f" name="更改" comment="发布管理">
      <change afterPath="$PROJECT_DIR$/.idea/CommonStatePersistent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/controller/gk/gkList.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/sqldialects.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/controller/message/lawsOperation.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/controller/message/lawsOperation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseCreateRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseCreateRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseUpdateRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/model/dto/message/FileReleaseUpdateRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/serviceImpl/message/FileReleaseService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/bjcj/xxgk/serviceImpl/message/FileReleaseService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/message/FileReleaseMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/message/FileReleaseMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1754637135" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1754637020" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\maven\apache-maven-3.9.9" />
        <option name="localRepository" value="C:\maven\maven-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="C:\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30ziFeeJ7aD1VR9PypM4dnqgGoh" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.performance-appraisal [clean].executor": "Run",
    "Maven.performance-appraisal [install].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.PerformanceAppraisalApplication.executor": "Run",
    "git-widget-placeholder": "dev",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/gitxm/xxgk",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDK",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.5897741",
    "settings.editor.selected.configurable": "SQL Dialects",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="xxgk" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="xxgk" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PerformanceAppraisalApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="performance-appraisal" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bjcj.xxgk.PerformanceAppraisalApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.22855.74" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.22855.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="23d2bed5-2b33-4bbe-9aab-6edeb00ded5f" name="更改" comment="" />
      <created>1754636701547</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754636701547</updated>
      <workItem from="1754636703852" duration="8413000" />
      <workItem from="1755476459595" duration="1869000" />
      <workItem from="1755821216963" duration="12418000" />
    </task>
    <task id="LOCAL-00001" summary="初始">
      <option name="closed" value="true" />
      <created>1754636913053</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754636913054</updated>
    </task>
    <task id="LOCAL-00002" summary="发布管理">
      <option name="closed" value="true" />
      <created>1756085726085</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1756085726085</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始" />
    <MESSAGE value="发布管理" />
    <option name="LAST_COMMIT_MESSAGE" value="发布管理" />
  </component>
</project>