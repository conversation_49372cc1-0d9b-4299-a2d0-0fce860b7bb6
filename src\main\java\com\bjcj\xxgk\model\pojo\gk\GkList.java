package com.bjcj.xxgk.model.pojo.gk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 征地信息公开列表
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "gk_list")
@Schema(description = "征地信息公开列表")
public class GkList implements Serializable {

    @TableId(value = "id")
    @Schema(description = "主键ID")
    private String id;

    @TableField(value = "instance_id")
    @Schema(description = "实例ID")
    private String instanceId;

    @TableField(value = "district")
    @Schema(description = "所属区")
    private String district;

    @TableField(value = "street")
    @Schema(description = "街道")
    private String street;

    @TableField(value = "unit")
    @Schema(description = "单位")
    private String unit;

    @TableField(value = "project_name")
    @Schema(description = "项目名称")
    private String projectName;

    @TableField(value = "approval_year")
    @Schema(description = "批准年度")
    private String approvalYear;

    @TableField(value = "approval_number")
    @Schema(description = "批准文号")
    private String approvalNumber;

    @TableField(value = "gk_time")
    @Schema(description = "公开时间")
    private String gkTime;

    @TableField(value = "type")
    @Schema(description = "业务类型")
    private String type;
}
