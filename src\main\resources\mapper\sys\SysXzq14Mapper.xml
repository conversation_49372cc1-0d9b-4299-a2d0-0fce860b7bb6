<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysXzq14Mapper">
    
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysXzq14">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="rural_code" jdbcType="VARCHAR" property="ruralCode"/>
        <result column="level" jdbcType="SMALLINT" property="level"/>
        <result column="p_id" jdbcType="BIGINT" property="pId"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, code, name, rural_code, level, p_id, sort
    </sql>

    <!-- 递归查询所有子级行政区编码 -->
    <select id="getChildrenCodesByCode" resultType="java.lang.String">
        WITH RECURSIVE children AS (
            -- 查找根节点
            SELECT id, code, name, p_id, level
            FROM sys_xzq14
            WHERE code = #{code}
            
            UNION ALL
            
            -- 递归查找子节点
            SELECT s.id, s.code, s.name, s.p_id, s.level
            FROM sys_xzq14 s
            INNER JOIN children c ON s.p_id = c.id
        )
        SELECT code FROM children
        ORDER BY level, code
    </select>

    <!-- 递归查询所有子级行政区 -->
    <select id="getChildrenByCode" resultMap="BaseResultMap">
        WITH RECURSIVE children AS (
            -- 查找根节点
            SELECT id, code, name, rural_code, level, p_id, sort
            FROM sys_xzq14
            WHERE code = #{code}
            
            UNION ALL
            
            -- 递归查找子节点
            SELECT s.id, s.code, s.name, s.rural_code, s.level, s.p_id, s.sort
            FROM sys_xzq14 s
            INNER JOIN children c ON s.p_id = c.id
        )
        SELECT <include refid="Base_Column_List"/>
        FROM children
        ORDER BY level, sort, code
    </select>

</mapper>
