package com.bjcj.xxgk.model.dto.gk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 征地信息公开列表查询参数DTO
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "征地信息公开列表查询参数")
public class GkListQueryDto {

    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;

    @Schema(description = "行政区编码（对应SysXzq14表中的code字段）", example = "140000")
    private String districtCode;

    @Schema(description = "项目名称（支持模糊查询）", example = "某某征地项目")
    private String projectName;

    @Schema(description = "批准年度", example = "2024")
    private String approvalYear;

    @Schema(description = "业务类型", example = "征地")
    private String type;

    @Schema(description = "单位（支持模糊查询）", example = "某某政府")
    private String unit;
}
