# 征地信息公开列表 API 使用说明

## 功能概述

本功能实现了征地信息公开列表的分页查询，支持按行政区编码进行层级查询，即查询指定行政区及其所有子级行政区的征地信息。

## 核心特性

1. **分页查询**：支持标准的分页参数
2. **行政区层级查询**：基于 SysXzq14 表的层级结构，支持递归查询子级行政区
3. **多条件筛选**：支持项目名称、批准年度、业务类型、单位等条件筛选
4. **参数校验**：完整的参数校验和错误处理
5. **Swagger 文档**：完整的 API 文档支持

## API 接口

### 1. 分页查询征地信息公开列表

**接口地址**：`GET /xxgk/gk/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNum | Integer | 是 | 页码，从1开始 | 1 |
| pageSize | Integer | 是 | 每页大小，1-100 | 10 |
| districtCode | String | 否 | 行政区编码 | 140100 |
| projectName | String | 否 | 项目名称（模糊查询） | 某某征地项目 |
| approvalYear | String | 否 | 批准年度 | 2024 |
| type | String | 否 | 业务类型 | 征地 |
| unit | String | 否 | 单位（模糊查询） | 某某政府 |

**请求示例**：
```
GET /xxgk/gk/list?pageNum=1&pageSize=10&districtCode=140100&approvalYear=2024
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": "gk001",
        "instanceId": "inst001",
        "district": "小店区",
        "street": "坞城街道",
        "unit": "太原市小店区政府",
        "projectName": "小店区某某地块征地项目",
        "approvalYear": "2024",
        "approvalNumber": "晋政征[2024]001号",
        "gkTime": "2024-01-15",
        "type": "征地"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 验证行政区编码

**接口地址**：`GET /xxgk/gk/list/validate-district-code`

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| districtCode | String | 是 | 行政区编码 | 140100 |

**请求示例**：
```
GET /xxgk/gk/list/validate-district-code?districtCode=140100
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

## 数据库设计

### 1. gk_list 表结构

```sql
CREATE TABLE "public"."gk_list" (
  "id" varchar(32) NOT NULL,
  "instance_id" varchar(255),
  "district" varchar(255),
  "street" varchar(255),
  "unit" varchar(255),
  "project_name" varchar(255),
  "approval_year" varchar(255),
  "approval_number" varchar(255),
  "gk_time" varchar(255),
  "type" varchar(255)
);
```

### 2. sys_xzq14 表结构

```sql
CREATE TABLE "public"."sys_xzq14" (
  "id" bigint NOT NULL,
  "code" varchar(255),
  "name" varchar(255),
  "rural_code" varchar(255),
  "level" smallint,
  "p_id" bigint,
  "sort" smallint
);
```

## 查询逻辑说明

### 行政区层级查询

当提供 `districtCode` 参数时，系统会：

1. 使用递归 CTE 查询指定行政区编码及其所有子级行政区编码
2. 将查询到的行政区编码列表用于筛选 gk_list 表中的数据
3. 通过 `district` 或 `street` 字段与 `sys_xzq14.name` 进行匹配

### 递归查询 SQL

```sql
WITH RECURSIVE children AS (
    -- 查找根节点
    SELECT id, code, name, p_id, level
    FROM sys_xzq14
    WHERE code = #{code}
    
    UNION ALL
    
    -- 递归查找子节点
    SELECT s.id, s.code, s.name, s.p_id, s.level
    FROM sys_xzq14 s
    INNER JOIN children c ON s.p_id = c.id
)
SELECT code FROM children
ORDER BY level, code
```

## 测试数据

项目根目录下的 `test_data.sql` 文件包含了完整的测试数据，包括：

- 山西省行政区划数据（省、市、区三级）
- 征地信息公开列表测试数据
- 测试查询示例

## 使用示例

### 1. 查询太原市所有征地信息

```
GET /xxgk/gk/list?pageNum=1&pageSize=10&districtCode=140100
```

这将返回太原市及其所有下级行政区（小店区、迎泽区等）的征地信息。

### 2. 查询特定项目

```
GET /xxgk/gk/list?pageNum=1&pageSize=10&projectName=征地项目
```

### 3. 按年度查询

```
GET /xxgk/gk/list?pageNum=1&pageSize=10&approvalYear=2024
```

## 错误处理

- 参数校验失败：返回 400 错误和具体错误信息
- 行政区编码不存在：返回 400 错误
- 系统异常：返回 500 错误和错误信息

## 性能优化建议

1. 在 `gk_list` 表的 `district`、`street`、`approval_year`、`gk_time` 字段上建立索引
2. 在 `sys_xzq14` 表的 `code`、`p_id` 字段上建立索引
3. 考虑将 `gk_time` 字段改为 `timestamp` 类型以提高查询性能

## 注意事项

1. 行政区编码必须在 `sys_xzq14` 表中存在
2. 分页大小限制在 1-100 之间
3. 查询结果按公开时间倒序排列
4. 支持模糊查询的字段：`projectName`、`unit`
