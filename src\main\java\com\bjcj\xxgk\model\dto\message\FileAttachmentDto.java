package com.bjcj.xxgk.model.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 文件附件DTO
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件附件信息")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FileAttachmentDto {

    @Schema(description = "文件ID")
    private String id;

    @Schema(description = "文件ID（用于关联）")
    private String fileId;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "是否为新上传文件")
    private Boolean isNew = false;
}
