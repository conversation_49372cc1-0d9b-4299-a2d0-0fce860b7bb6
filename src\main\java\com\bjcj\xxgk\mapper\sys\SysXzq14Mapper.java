package com.bjcj.xxgk.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.xxgk.model.pojo.sys.SysXzq14;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 山西省行政区代码表 Mapper 接口
 * <AUTHOR> Assistant
 */
public interface SysXzq14Mapper extends BaseMapper<SysXzq14> {

    /**
     * 根据行政区编码获取所有子级行政区编码（包括自身）
     * @param code 行政区编码
     * @return 行政区编码列表
     */
    List<String> getChildrenCodesByCode(@Param("code") String code);

    /**
     * 根据行政区编码获取所有子级行政区（包括自身）
     * @param code 行政区编码
     * @return 行政区列表
     */
    List<SysXzq14> getChildrenByCode(@Param("code") String code);
}
