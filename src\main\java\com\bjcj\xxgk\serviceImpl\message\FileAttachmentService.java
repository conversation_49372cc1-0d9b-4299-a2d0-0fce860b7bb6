package com.bjcj.xxgk.serviceImpl.message;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.FileInfo;
import com.bjcj.xxgk.common.enums.FileTypeEnum;
import com.bjcj.xxgk.common.utils.FileUtil;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.mapper.wflow.FileMapper;
import com.bjcj.xxgk.model.dto.message.FileAttachmentDto;
import com.bjcj.xxgk.model.pojo.wflow.File;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件附件服务类
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@Slf4j
public class FileAttachmentService extends ServiceImpl<FileMapper, File> {

    @Resource
    private FileMapper fileMapper;

    /**
     * 处理附件上传
     * @param files 上传的文件
     * @param instanceId 实例ID
     * @return 附件信息列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<FileAttachmentDto> handleFileUpload(MultipartFile[] files, String instanceId) {
        if (files == null || files.length == 0) {
            return new ArrayList<>();
        }

        List<FileAttachmentDto> attachments = new ArrayList<>();
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                FileAttachmentDto attachment = uploadSingleFile(file, instanceId);
                if (attachment != null) {
                    attachments.add(attachment);
                }
            }
        }
        return attachments;
    }

    /**
     * 上传单个文件
     * @param file 文件
     * @param instanceId 实例ID
     * @return 附件信息
     */
    private FileAttachmentDto uploadSingleFile(MultipartFile file, String instanceId) {
        try {
            // 使用项目现有的文件上传工具
            FileInfo fileInfo = FileUtil.uploadFile(file, FileTypeEnum.FILE);
            
            // 保存文件记录到数据库
            File fileEntity = File.builder()
                    .id(SnowflakeUtil.snowflakeId())
                    .instanceId(instanceId)
                    .fileId(SnowflakeUtil.snowflakeId())
                    .fileName(fileInfo.getFileName())
                    .fileType(getFileType(fileInfo.getSuffix()))
                    .filePath(fileInfo.getLocalUrl())
                    .build();
            
            save(fileEntity);
            
            // 转换为DTO
            return FileAttachmentDto.builder()
                    .id(fileEntity.getId())
                    .fileId(fileEntity.getFileId())
                    .fileName(fileEntity.getFileName())
                    .fileType(fileEntity.getFileType())
                    .filePath(fileEntity.getFilePath())
                    .fileSize(file.getSize())
                    .isNew(true)
                    .build();
                    
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据实例ID获取附件列表
     * @param instanceId 实例ID
     * @return 附件列表
     */
    public List<FileAttachmentDto> getAttachmentsByInstanceId(String instanceId) {
        if (StrUtil.isBlank(instanceId)) {
            return new ArrayList<>();
        }
        
        List<File> files = list(Wrappers.<File>lambdaQuery()
                .eq(File::getInstanceId, instanceId));
        
        return files.stream()
                .map(file -> BeanUtil.copyProperties(file, FileAttachmentDto.class))
                .collect(Collectors.toList());
    }

    /**
     * 保存附件关联
     * @param attachments 附件列表
     * @param instanceId 实例ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAttachments(List<FileAttachmentDto> attachments, String instanceId) {
        if (CollUtil.isEmpty(attachments)) {
            return;
        }
        
        for (FileAttachmentDto attachment : attachments) {
            if (attachment.getIsNew() != null && attachment.getIsNew()) {
                // 新上传的文件，更新实例ID
                File fileEntity = getById(attachment.getId());
                if (fileEntity != null) {
                    fileEntity.setInstanceId(instanceId);
                    updateById(fileEntity);
                }
            }
        }
    }

    /**
     * 删除附件
     * @param attachmentIds 附件ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAttachments(List<String> attachmentIds) {
        if (CollUtil.isEmpty(attachmentIds)) {
            return;
        }
        
        // 删除数据库记录
        removeByIds(attachmentIds);
        
        // TODO: 如果需要删除物理文件，可以在这里添加逻辑
        // 注意：删除物理文件需要谨慎，确保文件没有被其他地方引用
    }

    /**
     * 更新附件关联
     * @param attachments 新的附件列表
     * @param deleteAttachmentIds 要删除的附件ID列表
     * @param instanceId 实例ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAttachments(List<FileAttachmentDto> attachments, List<String> deleteAttachmentIds, String instanceId) {
        // 删除指定的附件
        if (CollUtil.isNotEmpty(deleteAttachmentIds)) {
            deleteAttachments(deleteAttachmentIds);
        }
        
        // 保存新的附件
        if (CollUtil.isNotEmpty(attachments)) {
            saveAttachments(attachments, instanceId);
        }
    }

    /**
     * 根据文件后缀获取文件类型
     * @param suffix 文件后缀
     * @return 文件类型
     */
    private String getFileType(String suffix) {
        if (StrUtil.isBlank(suffix)) {
            return "unknown";
        }
        
        suffix = suffix.toLowerCase();
        switch (suffix) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
                return "image";
            case "pdf":
                return "pdf";
            case "doc":
            case "docx":
                return "word";
            case "xls":
            case "xlsx":
                return "excel";
            case "ppt":
            case "pptx":
                return "powerpoint";
            case "txt":
                return "text";
            case "zip":
            case "rar":
            case "7z":
                return "archive";
            default:
                return "file";
        }
    }

    /**
     * 删除实例相关的所有附件
     * @param instanceId 实例ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAttachmentsByInstanceId(String instanceId) {
        if (StrUtil.isBlank(instanceId)) {
            return;
        }
        
        List<File> files = list(Wrappers.<File>lambdaQuery()
                .eq(File::getInstanceId, instanceId));
        
        if (CollUtil.isNotEmpty(files)) {
            List<String> fileIds = files.stream()
                    .map(File::getId)
                    .collect(Collectors.toList());
            deleteAttachments(fileIds);
        }
    }
}
