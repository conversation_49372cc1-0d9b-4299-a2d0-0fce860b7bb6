-- 测试数据初始化脚本
-- 用于测试征地信息公开列表查询功能

-- 1. 插入 sys_xzq14 测试数据（山西省行政区划）
INSERT INTO sys_xzq14 (id, code, name, rural_code, level, p_id, sort) VALUES
(1, '140000', '山西省', '1', 1, NULL, 1),
(2, '140100', '太原市', '1', 2, 1, 1),
(3, '140200', '大同市', '1', 2, 1, 2),
(4, '140101', '小店区', '1', 3, 2, 1),
(5, '140102', '迎泽区', '1', 3, 2, 2),
(6, '140103', '杏花岭区', '1', 3, 2, 3),
(7, '140201', '城区', '1', 3, 3, 1),
(8, '140202', '矿区', '1', 3, 3, 2),
(9, '140110', '尖草坪区', '1', 3, 2, 4),
(10, '140111', '万柏林区', '1', 3, 2, 5);

-- 2. 插入 gk_list 测试数据（征地信息公开列表）
INSERT INTO gk_list (id, instance_id, district, street, unit, project_name, approval_year, approval_number, gk_time, type) VALUES
('gk001', 'inst001', '小店区', '坞城街道', '太原市小店区政府', '小店区某某地块征地项目', '2024', '晋政征[2024]001号', '2024-01-15', '征地'),
('gk002', 'inst002', '迎泽区', '柳巷街道', '太原市迎泽区政府', '迎泽区商业用地征收项目', '2024', '晋政征[2024]002号', '2024-02-20', '征地'),
('gk003', 'inst003', '杏花岭区', '三桥街道', '太原市杏花岭区政府', '杏花岭区基础设施建设征地', '2023', '晋政征[2023]015号', '2024-01-10', '征地'),
('gk004', 'inst004', '尖草坪区', '古城街道', '太原市尖草坪区政府', '尖草坪区工业园区征地项目', '2024', '晋政征[2024]003号', '2024-03-05', '征地'),
('gk005', 'inst005', '万柏林区', '兴华街道', '太原市万柏林区政府', '万柏林区住宅用地征收', '2024', '晋政征[2024]004号', '2024-02-28', '征地'),
('gk006', 'inst006', '城区', '南关街道', '大同市城区政府', '城区旧城改造征地项目', '2023', '晋政征[2023]020号', '2024-01-25', '征地'),
('gk007', 'inst007', '矿区', '新胜街道', '大同市矿区政府', '矿区环境治理征地项目', '2024', '晋政征[2024]005号', '2024-03-10', '征地'),
('gk008', 'inst008', '小店区', '营盘街道', '太原市小店区政府', '小店区教育用地征收项目', '2024', '晋政征[2024]006号', '2024-03-15', '征地'),
('gk009', 'inst009', '迎泽区', '桥东街道', '太原市迎泽区政府', '迎泽区公园建设征地', '2023', '晋政征[2023]025号', '2024-02-05', '征地'),
('gk010', 'inst010', '杏花岭区', '鼓楼街道', '太原市杏花岭区政府', '杏花岭区医疗设施征地', '2024', '晋政征[2024]007号', '2024-03-20', '征地');

-- 查询验证数据
SELECT '=== sys_xzq14 数据 ===' as info;
SELECT * FROM sys_xzq14 ORDER BY level, sort;

SELECT '=== gk_list 数据 ===' as info;
SELECT * FROM gk_list ORDER BY gk_time DESC;

-- 测试查询示例
SELECT '=== 测试查询：太原市下所有征地信息 ===' as info;
WITH RECURSIVE children AS (
    SELECT id, code, name, p_id, level
    FROM sys_xzq14
    WHERE code = '140100'  -- 太原市
    
    UNION ALL
    
    SELECT s.id, s.code, s.name, s.p_id, s.level
    FROM sys_xzq14 s
    INNER JOIN children c ON s.p_id = c.id
)
SELECT gl.*, sx.name as district_name
FROM gk_list gl
JOIN sys_xzq14 sx ON (gl.district = sx.name OR gl.street = sx.name)
WHERE sx.code IN (SELECT code FROM children)
ORDER BY gl.gk_time DESC;
