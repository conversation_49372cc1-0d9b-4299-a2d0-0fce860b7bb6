package com.bjcj.xxgk.mapper.gk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bjcj.xxgk.model.pojo.gk.GkList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 征地信息公开列表 Mapper 接口
 * <AUTHOR> Assistant
 */
public interface GkListMapper extends BaseMapper<GkList> {

    /**
     * 根据行政区编码列表分页查询征地信息公开列表
     * @param page 分页参数
     * @param districtCodes 行政区编码列表
     * @param projectName 项目名称（可选）
     * @param approvalYear 批准年度（可选）
     * @param type 业务类型（可选）
     * @param unit 单位（可选）
     * @return 分页结果
     */
    IPage<GkList> selectPageByDistrictCodes(
            IPage<GkList> page,
            @Param("districtCodes") List<String> districtCodes,
            @Param("projectName") String projectName,
            @Param("approvalYear") String approvalYear,
            @Param("type") String type,
            @Param("unit") String unit
    );
}
