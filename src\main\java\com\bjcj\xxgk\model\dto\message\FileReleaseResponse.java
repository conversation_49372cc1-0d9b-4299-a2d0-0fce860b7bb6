package com.bjcj.xxgk.model.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件发布响应DTO
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件发布响应")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FileReleaseResponse {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "新闻/消息类型")
    private String newsType;

    @Schema(description = "文件/消息标题")
    private String fileHeader;

    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime releaseTime;

    @Schema(description = "发布者/作者")
    private String releaseUser;

    @Schema(description = "发布状态")
    private String status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "详细内容")
    private String details;

    @Schema(description = "附件文件列表")
    private List<FileAttachmentDto> attachments;
}
