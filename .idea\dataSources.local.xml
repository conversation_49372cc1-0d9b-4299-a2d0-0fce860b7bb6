<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-242.22855.74">
    <data-source name="xxgk@192.168.3.88" uuid="ee24d84e-b353-44a6-ac36-16b5d02b692c">
      <database-info product="PostgreSQL" version="12.12" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.7.3" dbms="POSTGRES" exact-version="12.12" exact-driver-version="42.7">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>postgres</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="database" qname="@">
            <node kind="schema" qname="@" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>