package com.bjcj.xxgk.serviceImpl.message;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.mapper.message.FileReleaseMapper;
import com.bjcj.xxgk.model.dto.message.FileAttachmentDto;
import com.bjcj.xxgk.model.dto.message.FileReleaseCreateRequest;
import com.bjcj.xxgk.model.dto.message.FileReleasePageRequest;
import com.bjcj.xxgk.model.dto.message.FileReleaseResponse;
import com.bjcj.xxgk.model.dto.message.FileReleaseUpdateRequest;
import com.bjcj.xxgk.model.pojo.message.FileRelease;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件发布服务实现类
 * 
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@Slf4j
public class FileReleaseService extends ServiceImpl<FileReleaseMapper, FileRelease> {

    @Resource
    private FileReleaseMapper fileReleaseMapper;

    @Resource
    private FileAttachmentService fileAttachmentService;

    /**
     * 创建文件发布
     *
     * @param request 创建请求
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult create(FileReleaseCreateRequest request) {
        FileRelease fileRelease = BeanUtil.copyProperties(request, FileRelease.class);
        String releaseId = SnowflakeUtil.snowflakeId();
        fileRelease.setId(releaseId);

        // 如果没有设置发布时间，默认为当前时间
        if (fileRelease.getReleaseTime() == null) {
            fileRelease.setReleaseTime(LocalDateTime.now());
        }

        boolean saved = save(fileRelease);
        if (saved) {
            // 处理附件关联
            if (CollUtil.isNotEmpty(request.getAttachments())) {
                fileAttachmentService.saveAttachments(request.getAttachments(), releaseId);
            }
            return JsonResult.success();
        } else {
            return JsonResult.error("创建失败");
        }
    }

    /**
     * 分页查询文件发布
     * 
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    public JsonResult page(Page<Object> page, FileReleasePageRequest request) {
        IPage<FileReleaseResponse> result = fileReleaseMapper.selectFileReleasePage(page, request);
        return JsonResult.success(result);
    }

    /**
     * 根据ID获取文件发布详情
     *
     * @param id 文件发布ID
     * @return 文件发布详情
     */
    public JsonResult getById(String id) {
        if (StrUtil.isBlank(id)) {
            return JsonResult.error("ID不能为空");
        }

        FileRelease fileRelease = getOne(Wrappers.<FileRelease>lambdaQuery().eq(FileRelease::getId, id));
        if (fileRelease == null) {
            return JsonResult.error("文件发布不存在");
        }

        FileReleaseResponse response = BeanUtil.copyProperties(fileRelease, FileReleaseResponse.class);

        // 获取附件信息
        List<FileAttachmentDto> attachments = fileAttachmentService.getAttachmentsByInstanceId(id);
        response.setAttachments(attachments);

        return JsonResult.success(response);
    }

    /**
     * 更新文件发布
     *
     * @param id      文件发布ID
     * @param request 更新请求
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(String id, FileReleaseUpdateRequest request) {
        if (StrUtil.isBlank(id)) {
            return JsonResult.error("ID不能为空");
        }

        FileRelease existingFileRelease = getOne(Wrappers.<FileRelease>lambdaQuery().eq(FileRelease::getId, id));
        if (existingFileRelease == null) {
            return JsonResult.error("文件发布不存在");
        }

        // 复制非空字段
        BeanUtil.copyProperties(request, existingFileRelease, "id", "createTime", "attachments", "deleteAttachmentIds");

        boolean updated = updateById(existingFileRelease);
        if (updated) {
            // 处理附件更新
            fileAttachmentService.updateAttachments(
                    request.getAttachments(),
                    request.getDeleteAttachmentIds(),
                    id);
            return JsonResult.success();
        } else {
            return JsonResult.error("更新失败");
        }
    }

    /**
     * 发布内容
     * 
     * @param id 文件发布ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult publish(String id) {
        return changeStatus(id, "已发布");
    }

    /**
     * 撤回发布
     * 
     * @param id 文件发布ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult withdraw(String id) {
        return changeStatus(id, "已撤回");
    }

    /**
     * 更改发布状态
     * 
     * @param id     文件发布ID
     * @param status 新状态
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult changeStatus(String id, String status) {
        if (StrUtil.isBlank(id)) {
            return JsonResult.error("ID不能为空");
        }

        if (StrUtil.isBlank(status)) {
            return JsonResult.error("状态不能为空");
        }

        FileRelease fileRelease = getOne(Wrappers.<FileRelease>lambdaQuery().eq(FileRelease::getId, id));
        if (fileRelease == null) {
            return JsonResult.error("文件发布不存在");
        }

        // 更新状态和发布时间
        fileRelease.setStatus(status);
        if ("已发布".equals(status)) {
            fileRelease.setReleaseTime(LocalDateTime.now());
        }

        boolean updated = updateById(fileRelease);
        return updated ? JsonResult.success("状态更新成功") : JsonResult.error("状态更新失败");
    }

    /**
     * 删除文件发布
     *
     * @param id 文件发布ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(String id) {
        if (StrUtil.isBlank(id)) {
            return JsonResult.error("ID不能为空");
        }

        FileRelease fileRelease = getOne(Wrappers.<FileRelease>lambdaQuery().eq(FileRelease::getId, id));
        if (fileRelease == null) {
            return JsonResult.error("文件发布不存在");
        }

        boolean deleted = removeById(id);
        if (deleted) {
            // 删除关联的附件
            fileAttachmentService.deleteAttachmentsByInstanceId(id);
            return JsonResult.success();
        } else {
            return JsonResult.error("删除失败");
        }
    }
}
